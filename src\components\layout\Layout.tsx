// 主布局组件

import React from 'react';
import Header from './Header';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="h-screen min-w-[1200px] min-h-[900px] overflow-hidden bg-primary-dark">
      <Header />
      <main className="px-24 py-4">
        {children}
      </main>
    </div>
  );
};

export default Layout;
