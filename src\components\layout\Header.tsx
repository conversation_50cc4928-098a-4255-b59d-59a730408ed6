// 顶部导航组件

import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// 本地图标资源
import logo from '../../assets/icons/logo.svg';

const Header: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const navigationItems = [
    { path: '/dashboard', label: '性能演示', key: 'dashboard' },
    { path: '/explorer', label: '数据浏览器', key: 'explorer' },
    { path: '/user-query', label: '用户查询', key: 'userQuery' },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="relative w-full h-20 bg-[#3c3c3c]">
      {/* Logo 区域 */}
      <div className="absolute left-[96px] top-[15px] flex items-center">
        {/* Logo 图标组 - 使用合并的 Logo */}
        <div className="relative w-[45px] h-[50px]">
          <img
            alt="Logo"
            className="block max-w-none size-full"
            src={logo}
          />
        </div>

        {/* 分隔线 */}
        <div className="ml-5 flex items-center justify-center w-0 h-0">
              <div className=" left-0 right-0  h-[24px]  border-l-2 border-[#679cff]">
              
              </div>
        </div>

        {/* 标题文字 */}
        <div className="ml-5">
          <p
            className="text-[#ffffff] text-[18px] font-normal leading-normal"
            style={{ fontFamily: 'PingFang SC' }}
          >
            高性能计算引擎演示
          </p>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="absolute right-8 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
        {/* {navigationItems.map((item) => (
          <button
            key={item.key}
            onClick={() => navigate(item.path)}
            className={`px-6 py-2 rounded-lg transition-colors ${
              isActive(item.path)
                ? 'bg-primary-blue text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            }`}
            style={{ fontFamily: 'PingFang SC' }}
          >
            {item.label}
          </button>
        ))} */}
      </nav>
    </div>
  );
};

export default Header;
